{"name": "@changesets/config", "version": "3.1.1", "description": "Utilities for reading and parsing <PERSON><PERSON>'s config", "main": "dist/changesets-config.cjs.js", "module": "dist/changesets-config.esm.js", "exports": {".": {"types": {"import": "./dist/changesets-config.cjs.mjs", "default": "./dist/changesets-config.cjs.js"}, "module": "./dist/changesets-config.esm.js", "import": "./dist/changesets-config.cjs.mjs", "default": "./dist/changesets-config.cjs.js"}, "./package.json": "./package.json"}, "license": "MIT", "repository": "https://github.com/changesets/changesets/tree/main/packages/config", "files": ["dist", "schema.json"], "dependencies": {"@changesets/errors": "^0.2.0", "@changesets/get-dependents-graph": "^2.1.3", "@changesets/logger": "^0.1.1", "@changesets/types": "^6.1.0", "@manypkg/get-packages": "^1.1.3", "fs-extra": "^7.0.1", "micromatch": "^4.0.8"}, "devDependencies": {"@changesets/test-utils": "*", "@types/micromatch": "^4.0.1", "jest-in-case": "^1.0.2", "outdent": "^0.5.0"}}