{"name": "@changesets/apply-release-plan", "version": "7.0.12", "description": "Takes a release plan and applies it to packages", "main": "dist/changesets-apply-release-plan.cjs.js", "module": "dist/changesets-apply-release-plan.esm.js", "exports": {".": {"types": {"import": "./dist/changesets-apply-release-plan.cjs.mjs", "default": "./dist/changesets-apply-release-plan.cjs.js"}, "module": "./dist/changesets-apply-release-plan.esm.js", "import": "./dist/changesets-apply-release-plan.cjs.mjs", "default": "./dist/changesets-apply-release-plan.cjs.js"}, "./package.json": "./package.json"}, "license": "MIT", "repository": "https://github.com/changesets/changesets/tree/main/packages/apply-release-plan", "dependencies": {"@changesets/config": "^3.1.1", "@changesets/get-version-range-type": "^0.4.0", "@changesets/git": "^3.0.4", "@changesets/should-skip-package": "^0.1.2", "@changesets/types": "^6.1.0", "@manypkg/get-packages": "^1.1.3", "detect-indent": "^6.0.0", "fs-extra": "^7.0.1", "lodash.startcase": "^4.4.0", "outdent": "^0.5.0", "prettier": "^2.7.1", "resolve-from": "^5.0.0", "semver": "^7.5.3"}, "devDependencies": {"@changesets/test-utils": "*", "spawndamnit": "^3.0.1"}}