"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.TYPE_VALUE = exports.VALUE = exports.TYPE = void 0;
exports.TYPE = Object.freeze({
    eslintImplicitGlobalSetting: 'readonly',
    isTypeVariable: true,
    isValueVariable: false,
});
exports.VALUE = Object.freeze({
    eslintImplicitGlobalSetting: 'readonly',
    isTypeVariable: false,
    isValueVariable: true,
});
exports.TYPE_VALUE = Object.freeze({
    eslintImplicitGlobalSetting: 'readonly',
    isTypeVariable: true,
    isValueVariable: true,
});
//# sourceMappingURL=base-config.js.map