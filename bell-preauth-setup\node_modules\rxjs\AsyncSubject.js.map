{"version": 3, "file": "AsyncSubject.js", "sourceRoot": "", "sources": ["../src/AsyncSubject.ts"], "names": [], "mappings": ";;;;;;AAAA,wBAAwB,WAAW,CAAC,CAAA;AAEpC,6BAA6B,gBAAgB,CAAC,CAAA;AAE9C;;GAEG;AACH;IAAqC,gCAAU;IAA/C;QAAqC,8BAAU;QACrC,UAAK,GAAM,IAAI,CAAC;QAChB,YAAO,GAAY,KAAK,CAAC;QACzB,iBAAY,GAAY,KAAK,CAAC;IAkCxC,CAAC;IAhCC,oCAAoC,CAAC,iCAAU,GAAV,UAAW,UAA2B;QACzE,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YAClB,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACnC,MAAM,CAAC,2BAAY,CAAC,KAAK,CAAC;QAC5B,CAAC;QAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;YAC7C,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5B,UAAU,CAAC,QAAQ,EAAE,CAAC;YACtB,MAAM,CAAC,2BAAY,CAAC,KAAK,CAAC;QAC5B,CAAC;QACD,MAAM,CAAC,gBAAK,CAAC,UAAU,YAAC,UAAU,CAAC,CAAC;IACtC,CAAC;IAED,2BAAI,GAAJ,UAAK,KAAQ;QACX,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;YACvB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;YACnB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACtB,CAAC;IACH,CAAC;IAED,4BAAK,GAAL,UAAM,KAAU;QACd,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;YACvB,gBAAK,CAAC,KAAK,YAAC,KAAK,CAAC,CAAC;QACrB,CAAC;IACH,CAAC;IAED,+BAAQ,GAAR;QACE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;YACjB,gBAAK,CAAC,IAAI,YAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzB,CAAC;QACD,gBAAK,CAAC,QAAQ,WAAE,CAAC;IACnB,CAAC;IACH,mBAAC;AAAD,CAAC,AArCD,CAAqC,iBAAO,GAqC3C;AArCY,oBAAY,eAqCxB,CAAA", "sourcesContent": ["import { Subject } from './Subject';\nimport { Subscriber } from './Subscriber';\nimport { Subscription } from './Subscription';\n\n/**\n * @class AsyncSubject<T>\n */\nexport class AsyncSubject<T> extends Subject<T> {\n  private value: T = null;\n  private hasNext: boolean = false;\n  private hasCompleted: boolean = false;\n\n  /** @deprecated internal use only */ _subscribe(subscriber: Subscriber<any>): Subscription {\n    if (this.hasError) {\n      subscriber.error(this.thrownError);\n      return Subscription.EMPTY;\n    } else if (this.hasCompleted && this.hasNext) {\n      subscriber.next(this.value);\n      subscriber.complete();\n      return Subscription.EMPTY;\n    }\n    return super._subscribe(subscriber);\n  }\n\n  next(value: T): void {\n    if (!this.hasCompleted) {\n      this.value = value;\n      this.hasNext = true;\n    }\n  }\n\n  error(error: any): void {\n    if (!this.hasCompleted) {\n      super.error(error);\n    }\n  }\n\n  complete(): void {\n    this.hasCompleted = true;\n    if (this.hasNext) {\n      super.next(this.value);\n    }\n    super.complete();\n  }\n}\n"]}