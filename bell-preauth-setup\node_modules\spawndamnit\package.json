{"name": "spawndamnit", "version": "3.0.1", "main": "index.js", "description": "Take care of your `spawn()`", "author": "<PERSON> <<EMAIL>>", "repository": "https://github.com/thejameskyle/spawndamnit", "license": "SEE LICENSE IN LICENSE", "keywords": ["spawn", "child", "process", "promise", "async", "cross-spawn"], "files": ["index.js", "promise.js", "promise.js.flow", "error.js"], "scripts": {"flow": "flow", "test": "ava"}, "dependencies": {"cross-spawn": "^7.0.5", "signal-exit": "^4.0.1"}, "devDependencies": {"ava": "^4.3.3", "fixturez": "^1.1.0", "flow-bin": "^0.63.1"}}