{"name": "@types/enzyme", "version": "3.10.5", "description": "TypeScript definitions for Enzyme", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/MarianPalkus", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Cap3", "url": "http://www.cap3.de"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/NoHomey", "githubUsername": "NoHomey"}, {"name": "jwbay", "url": "https://github.com/jwbay", "githubUsername": "jwbay"}, {"name": "huhuanming", "url": "https://github.com/huhuanming", "githubUsername": "huhuanming"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/MartynasZilinskas", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Torge<PERSON>", "url": "https://github.com/thovden", "githubUsername": "thovden"}, {"name": "<PERSON>", "url": "https://github.com/hotell", "githubUsername": "hotell"}, {"name": "<PERSON>", "url": "https://github.com/screendriver", "githubUsername": "screendriver"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/mateuszsokola", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/braidencutforth", "githubUsername": "braidencutforth"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/erickzhao", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/j<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ljharb", "githubUsername": "lj<PERSON>b"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/enzyme"}, "scripts": {}, "dependencies": {"@types/cheerio": "*", "@types/react": "*"}, "typesPublisherContentHash": "7509e67a87396c646fb9d5638cf41eea464485f593eb6c22fdcdd2e116db4055", "typeScriptVersion": "3.1"}