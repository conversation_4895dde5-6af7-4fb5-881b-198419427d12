# better-path-resolve

> A better path.resolve() that normalizes paths on Windows

<!--@shields('npm')-->
[![npm version](https://img.shields.io/npm/v/better-path-resolve.svg)](https://www.npmjs.com/package/better-path-resolve)
<!--/@-->

## Installation

```sh
npm i -S better-path-resolve
```

## Usage

```js
'use strict'
const betterPathResolve = require('better-path-resolve')

console.log(betterPathResolve('c:/src'))
//> C:\src
```

## License

[MIT](./LICENSE) © [<PERSON><PERSON><PERSON>](https://www.kochan.io)
