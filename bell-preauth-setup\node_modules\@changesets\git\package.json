{"name": "@changesets/git", "version": "3.0.4", "description": "Some git helpers that changesets use to get information", "main": "dist/changesets-git.cjs.js", "module": "dist/changesets-git.esm.js", "exports": {".": {"types": {"import": "./dist/changesets-git.cjs.mjs", "default": "./dist/changesets-git.cjs.js"}, "module": "./dist/changesets-git.esm.js", "import": "./dist/changesets-git.cjs.mjs", "default": "./dist/changesets-git.cjs.js"}, "./package.json": "./package.json"}, "license": "MIT", "repository": "https://github.com/changesets/changesets/tree/main/packages/git", "dependencies": {"@changesets/errors": "^0.2.0", "@manypkg/get-packages": "^1.1.3", "is-subdir": "^1.1.1", "micromatch": "^4.0.8", "spawndamnit": "^3.0.1"}, "devDependencies": {"@changesets/test-utils": "*", "@changesets/write": "*", "file-url": "^3.0.0", "fs-extra": "^7.0.1"}}