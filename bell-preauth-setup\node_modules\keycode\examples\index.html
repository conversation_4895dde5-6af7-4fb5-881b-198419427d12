<link href="//netdna.bootstrapcdn.com/twitter-bootstrap/2.2.1/css/bootstrap.no-responsive.no-icons.min.css" rel="stylesheet">
<div id="keyname">Press any key.</div>
<script src="../build/build.js"></script>
<script>
  var keycode = require('keycode')
  window.keycode =  keycode
  console.log("The keyCode for 'Enter' is %d.", keycode('Enter'))
  window.addEventListener('keydown', keydown);
  function keydown(e) {
    document.getElementById('keyname').innerHTML = (e.which ||
      e.keyCode) + ': ' + keycode(e.which || e.keyCode);
  }
</script>
