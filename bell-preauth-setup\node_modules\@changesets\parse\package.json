{"name": "@changesets/parse", "version": "0.4.1", "description": "Parse a changeset file's contents into a usable json object", "main": "dist/changesets-parse.cjs.js", "module": "dist/changesets-parse.esm.js", "exports": {".": {"types": {"import": "./dist/changesets-parse.cjs.mjs", "default": "./dist/changesets-parse.cjs.js"}, "module": "./dist/changesets-parse.esm.js", "import": "./dist/changesets-parse.cjs.mjs", "default": "./dist/changesets-parse.cjs.js"}, "./package.json": "./package.json"}, "license": "MIT", "repository": "https://github.com/changesets/changesets/tree/main/packages/parse", "dependencies": {"@changesets/types": "^6.1.0", "js-yaml": "^3.13.1"}, "devDependencies": {"outdent": "^0.5.0"}}