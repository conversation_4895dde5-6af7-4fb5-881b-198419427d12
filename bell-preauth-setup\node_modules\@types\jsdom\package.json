{"name": "@types/jsdom", "version": "16.2.0", "description": "TypeScript definitions for jsdom", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/leonard-thieu", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"name": "<PERSON>", "url": "https://github.com/palmfjord", "githubUsername": "palmfjord"}, {"name": "ExE Boss", "url": "https://github.com/ExE-Boss", "githubUsername": "ExE-Boss"}], "main": "", "types": "index.d.ts", "typesVersions": {">=3.1.0-0": {"*": ["ts3.1/*"]}, ">=3.4.0-0": {"*": ["ts3.4/*"]}, ">=3.5.0-0": {"*": ["ts3.5/*"]}, ">=3.6.0-0": {"*": ["ts3.6/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/jsdom"}, "scripts": {}, "dependencies": {"@types/node": "*", "@types/parse5": "*", "@types/tough-cookie": "*"}, "typesPublisherContentHash": "faa8b1fdb18b1904aac57af851b806972afb942fc2d87288db93e985c1ea8b85", "typeScriptVersion": "3.0"}