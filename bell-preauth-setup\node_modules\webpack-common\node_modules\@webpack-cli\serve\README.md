# webpack-cli serve

[![NPM Downloads][downloads]][downloads-url]

> **Note**
>
> This package is used by webpack-cli under-the-hood and is not intended for installation

## Description

This package contains the logic to run [webpack-dev-server](https://github.com/webpack/webpack-dev-server) to serve your webpack app and provide live reloading.

## Installation

```bash
npm i -D webpack-cli @webpack-cli/serve
```

## Usage

### CLI (via `webpack-cli`)

```bash
npx webpack-cli serve
```

### Options

Checkout [`SERVE-OPTIONS-v4.md`](https://github.com/webpack/webpack-cli/blob/master/SERVE-OPTIONS-v4.md) or [`SERVE-OPTIONS-v5.md`](https://github.com/webpack/webpack-cli/blob/master/SERVE-OPTIONS-v5.md) to see list of all available options for `serve` command for respective [`webpack-dev-server`](https://github.com/webpack/webpack-dev-server) version.

[downloads]: https://img.shields.io/npm/dm/@webpack-cli/serve.svg
[downloads-url]: https://www.npmjs.com/package/@webpack-cli/serve
